import { ChangeDetectionStrategy, Component, OnInit, inject, TemplateRef, ViewChild } from '@angular/core';
import { FlashMessageService } from '@core/services/flash_message.service';
import { TranslateService } from '@ngx-translate/core';
import { PromotionModalService } from '@features/sales/order-form/components/modals/promotion-modal';
import { VariantFormModalService } from '@shared/modals/product/variant-form-modal';
import { MatDialog } from '@angular/material/dialog';
import { NoteModalService } from '@features/sales/order-form/components/modals/note-modal';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';
import { MixedPaymentModalService } from '@features/sales/order-form/components/modals/mixed-payment-modal/mixed-payment-modal.service';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { OrderItemModifierModalService } from '@shared/modals/sales/order/order-item-modifier-modal/order-item-modifier-modal.service';
import { mockProductSelectorConfig } from '@mock/sales/order_form.mock';
import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { OrderItemVariantUnitSelectionModalService } from '@shared/modals/sales/order/order-item-variant-unit-selection-modal';
import { OrderProductPickerComponent } from '@shared/components/product-selection/order-product-picker/order-product-picker.component';
import { InputPlaceComponent } from '../input/input-place/input-place.component';
import { AddressManualSelectorComponent } from '../input/input-place/components/address-manual-selector/address-manual-selector.component';
import { InputAddressComponent } from '../input/input-place/components/input-address/input-address.component';
import { DynamicLayoutBuilderComponent } from '../dynamic-layout-builder/dynamic-layout-builder.component';
import { simpleMockLayouts } from '@mock/product/simple_layout.mock';
import { mockProductModifierGroupList, mockProductUnits, mockVariantList, mockProductList } from '@mock/product/product.mock';
import { BatchModalComponent, BatchData } from '@shared/modals/warehouse/batch-modal/batch-modal.component';
import { CategoryProductModalComponent } from '@features/warehouse/goods-receipt/components/category-product-modal/category-product-modal.component';
import { CategoryProductModalData } from '@features/warehouse/goods-receipt/models/view/goods-receipt.view-model';
import { AdditionalCostModalService } from '@features/warehouse/goods-receipt/components/additional-cost-modal/additional-cost-modal.service';
import { TaxFormModalService } from '@shared/modals/common/tax-form-modal';
import { TaxInfo } from 'salehub_shared_contracts/entities/ims/inventory/goods_receipt';
import { QualityCheckRejectModalComponent, RejectedItem, QualityCheckRejectModalData } from '@features/warehouse/goods-receipt/components/quality-check-reject-modal/quality-check-reject-modal.component';
import { SimpleNoteModalService } from '@shared/modals/common/simple-note-modal';
import { ProductFilterModalComponent } from '@features/warehouse/inventory-check/components/product-filter-modal/product-filter-modal.component';
import { FieldPermissionModalService } from '@/shared/modals/field-permission';
import { ProductFilterModalData, EmbeddedProductSerial, ProductListItem } from '@features/warehouse/inventory-check/models/api/inventory-check.dto';
import { ProductFilterResult } from '@features/warehouse/inventory-check/models/view/inventory-check.view-model';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';
import { ConfirmModalService } from '@shared/modals/common/confirm-modal';
import { FieldPropertiesData, FieldPropertiesModalComponent } from '@/shared/components/dynamic-layout-builder/modals/field-properties/field-properties-modal.component';
import { FieldFiltersComponent } from '../field-filters/field-filters.component';
import { Field } from '@domain/entities/field.entity';
import { FilterChangeEvent, FieldFilter } from '../field-filters/models/view/field-filter-view.model';
import { mockCustomerFields } from '@mock/fields.mock';
import { StandardDialogService, StandardDialogResult } from '../standard-dialog';
import { SimpleTestComponent } from '../standard-dialog/test-components/simple-test.component';
import { StandardBottomSheetService, StandardBottomSheetResult } from '../standard-bottom-sheet';
import { CreateLayoutModalService } from '../dynamic-layout-builder/modals/create-layout-modal/create-layout-modal.service';
import { CreateLayoutModalResult } from '../dynamic-layout-builder/modals/create-layout-modal/create-layout-modal.component';
import {
  SimpleBottomSheetTestComponent,
  SimpleBottomSheetTestData,
  SimpleBottomSheetTestResult
} from '../standard-bottom-sheet/test-components/simple-bottom-sheet-test.component';
import { ListColumnSelectorModalComponent } from '@shared/modals/common/list-column-selector-modal/list-column-selector-modal.component';
import { ListColumnConfig } from '@shared/models/view/list-layout.model';
import { ValidationTestComponent, ValidationTestData, ValidationTestResult } from '../standard-dialog/test-components/validation-test.component';
import { StrictTestModalComponent, StrictTestModalData, StrictTestModalResult } from './components/strict-test-modal.component';
import { ResponsiveModalConfig } from '@core/services/responsive-modal.service';
import { SettingsListComponent, SettingChangeEvent, SettingsListConfig, SettingsSaveEvent, AutoSaveToggleEvent } from '../settings/settings-list.component';
import { mockAllSettings, mockDefaultSettingValues } from '@mock/settings/all-settings.mock';
import { IntegrationAccountViewModalComponent } from '@features/settings/integrations/components/integration-new/modals/integration-account-view-modal/integration-account-view-modal.component';
import { IntegrationAccountViewModalData, IntegrationAccountViewModalResult } from '@features/settings/integrations/components/integration-new/modals/integration-account-view-modal/integration-account-view-modal.interfaces';
import { IntegrationAccountSettingsModalComponent } from '@features/settings/integrations/components/integration-new/modals/integration-account-settings-modal/integration-account-settings-modal.component';
import { IntegrationAccountSettingsModalData, IntegrationAccountSettingsModalResult } from '@features/settings/integrations/components/integration-new/modals/integration-account-settings-modal/integration-account-settings-modal.interfaces';
import { IntegrationAccountLogsModalComponent } from '@features/settings/integrations/components/integration-new/modals/integration-account-logs-modal/integration-account-logs-modal.component';
import { IntegrationAccountLogsModalData, IntegrationAccountLogsModalResult } from '@features/settings/integrations/components/integration-new/modals/integration-account-logs-modal/integration-account-logs-modal.interfaces';
import { getMockLogsByPlatform } from '@/mock/settings/integration_view_account.mock';

/**
 * Interface cho kết quả test modal với action và data
 */
export interface TestModalResult {
  action: 'confirm' | 'cancel' | 'custom';
  data?: unknown;
  customAction?: {
    name: string;
    value: unknown;
  };
}

@Component({
  selector: 'app-test-theme',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    InputPlaceComponent,
    DynamicLayoutBuilderComponent,
    SettingsListComponent
    // OrderProductPickerComponent
  ],
  providers: [
    // Provide CreateLayoutModalService để tránh NullInjectorError
    CreateLayoutModalService
  ],
  templateUrl: './test-theme.component.html',
  styleUrl: './test-theme.component.scss'
})
export class TestThemeComponent implements OnInit {
  // Sử dụng property injection thay vì constructor
  private dialog = inject(MatDialog);
  private bottomSheet = inject(MatBottomSheet);
  private responsiveModalService = inject(ResponsiveModalService);
  private flashMessageService = inject(FlashMessageService);

  // Test layout configs for Dynamic Layout Builder - sử dụng simple mock data
  testLayoutBuilderConfig = {
    layoutId: simpleMockLayouts[0]._id || 'layout-1',
    layouts: simpleMockLayouts,
    defaultLayoutConfig: simpleMockLayouts[0] // Simple layout làm default
  };
  private translateService = inject(TranslateService);
  private mixedPaymentModalService = inject(MixedPaymentModalService);
  private productModifierFormModalService = inject(OrderItemModifierModalService);
  private orderItemVariantUnitSelectionModalService = inject(OrderItemVariantUnitSelectionModalService);
  private noteModalService = inject(NoteModalService);
  private promotionModalService = inject(PromotionModalService);
  private variantFormModalService = inject(VariantFormModalService);
  private simpleNoteModalService = inject(SimpleNoteModalService);
  private fieldPermissionModalService = inject(FieldPermissionModalService);
  private taxFormModalService = inject(TaxFormModalService);
  private confirmModalService = inject(ConfirmModalService);
  private standardDialogService = inject(StandardDialogService);
  private standardBottomSheetService = inject(StandardBottomSheetService);
  private createLayoutModalService = inject(CreateLayoutModalService);

  // Dữ liệu mẫu cho ProductSelector
  productSelectorConfig = mockProductSelectorConfig;

  selectedProducts: OrderItemBaseDetails[] = [];

  // Mock data cho Field Filters
  mockFields = mockCustomerFields;

  // Mock data cho Settings
  settingsConfig: SettingsListConfig = {
    settings: mockAllSettings,
    values: { ...mockDefaultSettingValues },
    showDividers: true,
    showCard: false,
    containerClass: '',
    autoSave: false, // Thay đổi thành false để test save/cancel functionality
    showSaveButtons: true
  };

  // ViewChild cho custom templates
  @ViewChild('customTitleTemplate', { static: true }) customTitleTemplate!: TemplateRef<any>;
  @ViewChild('customActionsTemplate', { static: true }) customActionsTemplate!: TemplateRef<any>;

  ngOnInit(): void {
  }

  /**
   * Xử lý khi setting thay đổi
   */
  onSettingChange(event: SettingChangeEvent): void {
    console.log('Setting changed:', event);

    // Hiển thị thông báo về thay đổi setting
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.SETTINGS.SETTING_UPDATED'),
      {
        description: `${event.setting.name}: ${this.getSettingValueDisplay(event)}`
      }
    );
  }

  /**
   * Xử lý khi lưu settings
   */
  onSettingsSave(event: SettingsSaveEvent): void {
    console.log('Settings saved:', event);

    // Hiển thị thông báo lưu thành công
    this.flashMessageService.success(
      this.translateService.instant('SETTINGS.SAVE_SUCCESS'),
      {
        description: `Đã lưu ${Object.keys(event.changedSettings).length} cài đặt`
      }
    );
  }

  /**
   * Xử lý khi hủy thay đổi settings
   */
  onSettingsCancel(): void {
    console.log('Settings cancelled');

    // Hiển thị thông báo hủy thành công
    this.flashMessageService.info(
      this.translateService.instant('SETTINGS.CANCEL_SUCCESS')
    );
  }

  /**
   * Xử lý khi toggle auto save
   */
  onAutoSaveToggle(event: AutoSaveToggleEvent): void {
    console.log('Auto save toggled:', event);

    // Hiển thị thông báo thay đổi auto save
    const messageKey = event.enabled ? 'SETTINGS.AUTO_SAVE_ENABLED' : 'SETTINGS.AUTO_SAVE_DISABLED';
    this.flashMessageService.info(
      this.translateService.instant(messageKey)
    );
  }

  /**
   * Lấy text hiển thị cho giá trị setting
   */
  private getSettingValueDisplay(event: SettingChangeEvent): string {
    if (event.setting.type === 'toggle') {
      return event.value ? 'Bật' : 'Tắt';
    }

    if (event.setting.type === 'select' || event.setting.type === 'radio') {
      const option = event.setting.options?.find(opt => opt.value === event.value);
      return option?.label || event.value;
    }

    return String(event.value);
  }

  async openProductModifiersSheet(): Promise<void> {
    // Tạo mock dữ liệu cho OrderItemBaseDetails
    const orderItem: OrderItemBaseDetails = {
      quantity: 1,
      product: {
        productId: 'prod1',
        cost: 150000,
        name: 'Phở bò tái lăn',
        price: 200000,
        variant: {
          variantId: 'v1',
          attributes: [{ name: 'size', value: 'L' }, { name: 'color', value: 'Xanh' }]
        }
      }
    };

    try {
      // Mở modal chọn modifier sử dụng service
      const result = await this.productModifierFormModalService.open({
        list: mockProductModifierGroupList,
        data: orderItem
      });

      if (result) {
        console.log('Product modifier modal result:', result);
      }
    } catch (error: any) {
      // Thay thế console.error bằng FlashMessageService cho test component
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: `Lỗi khi mở modal chọn modifier: ${error.message}`
        }
      );
    }
  }

  async openMixedPaymentDialog(): Promise<void> {
    // Sample data
    const orderPayment: OrderPayment = {
      totalAmount: 1625000,
      payments: [],
      paidAmount: 0,
      remainingAmount: 1625000,
      paymentStatus: 'unpaid'
    };

    try {
      const result = await this.mixedPaymentModalService.open(orderPayment);
      if (result) {
        console.log('Modal result:', result);
      }
    } catch (error: any) {
      // Thay thế console.error bằng FlashMessageService cho test component
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: `Error opening mixed payment modal: ${error.message}`
        }
      );
    }
  }

  openVariantSelector() {
    // Mở modal chọn variant và unit sử dụng service
    this.orderItemVariantUnitSelectionModalService.open({
      variants: mockVariantList,
      currentValue: {
        variant: mockVariantList[1], // Chọn variant thứ 2 (L, Đỏ)
        unit: mockProductUnits[1] // Chọn đơn vị vỉ
      },
      units: mockProductUnits
    }).then(result => {
      if (result) {
        console.log('Selected variant:', result.variant);
        console.log('Selected unit:', result.unit);
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn variant và unit:', error);
    });
  }

  /**
   * Mở modal form thuộc tính sản phẩm
   */
  async openVariantFormModal(): Promise<void> {
    try {
      const result = await this.variantFormModalService.open({
        valueSuggestions: ['S', 'M', 'L', 'XL', 'Đỏ', 'Xanh', 'Đen', 'Trắng']
      });

      if (result) {
        console.log('Variant form modal result:', result);
        alert(`Thuộc tính đã được tạo: ${result.name} với ${result.values.length} giá trị`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal form thuộc tính sản phẩm:', error);
    }
  }

  openProductForm(): void {
    console.log('Open product form dialog');
    // Placeholder for opening the product form dialog
    // Trong triển khai thực tế, sẽ mở dialog product-form
  }

  openBatchDialog(): void {
    this.responsiveModalService.open<
      BatchData,
      BatchData,
      BatchModalComponent
    >(
      BatchModalComponent,
      {
        data: {
          batchNumber: '',
          manufacturingDate: new Date(),
          expiryDate: null,
          quantity: null
        }
      }
    ).then(result => {
      if (result) {
        console.log('Batch dialog result:', result);
      }
    }).catch(error => {
      console.error('Error in batch modal:', error);
    });
  }

  openCategoryProductDialog(): void {
    this.responsiveModalService
      .open<CategoryProductModalData, ProductListItem[], CategoryProductModalComponent>(
        CategoryProductModalComponent,
        { width: '450px', disableClose: false }
      )
      .then(result => {
        console.log('Selected products:', result);
        if (result) {
          alert(`Đã chọn ${result.length} sản phẩm từ nhóm hàng`);
        }
      })
      .catch(() => {});
  }

  /**
   * Mở dialog thêm/sửa chi phí
   */
  async openAdditionalCostDialog(isEdit: boolean = false): Promise<void> {
    // Khởi tạo dữ liệu mẫu chi phí
    const mockCost: ImportAdditionalCost = {
      _id: 'cost1',
      name: 'Phí vận chuyển',
      costValue: {
        type: 'fixed',
        value: 250000
      },
      paidToSupplier: true,
      allocateToItems: true,
      isActive: true,
      autoAddToPurchaseOrder: false,
      refundOnReturn: true,
      tax: {
        rate: 10,
        amount: 25000
      }
    };

    const additionalCostModalService = inject(AdditionalCostModalService);
    const result = await additionalCostModalService.open({
      cost: isEdit ? mockCost : undefined,
      subTotal: 5000000
    });

    if (result) {
      console.log('Additional cost dialog result:', result);
      alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} chi phí: ${result.name}`);
    }
  }

  // Test SelectAdditionalCostsDialog
  openSelectAdditionalCostsDialog(): void {
    // // Khởi tạo dữ liệu mẫu đã chọn
    // const selectedCosts: ImportAdditionalCost[] = [
    //   mockImportAdditionalCosts[0],
    //   mockImportAdditionalCosts[2]
    // ];

    // const dialogRef = this.dialog.open(SelectAdditionalCostsDialogComponent, {
    //   width: '900px',
    //   data: {
    //     items: mockImportAdditionalCosts,
    //     current: selectedCosts,
    //     subTotal: 5000000
    //   }
    // });

    // dialogRef.afterClosed().subscribe((result: ImportAdditionalCost[] | undefined) => {
    //   if (result) {
    //     console.log('Selected additional costs:', result);
    //     alert(`Đã chọn ${result.length} chi phí khác`);
    //   }
    // });
  }

  // Test TaxFormModal
  async openTaxDialog(isEdit: boolean = false): Promise<void> {
    const mockTax: TaxInfo = {
      type: 'VAT',
      rate: 10,
      amount: 500000
    };

    try {
      const result = await this.taxFormModalService.open({
        tax: isEdit ? mockTax : undefined,
        subTotal: 5000000
      });

      if (result) {
        console.log('Tax modal result:', result);
        alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} thuế: ${result.type} với số tiền ${result.amount} VND`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thuế:', error);
    }
  }

  // Test QualityCheckRejectModal
  async openQualityCheckRejectDialog(isEdit: boolean = false): Promise<void> {
    // Tạo mock data cho GoodsReceiptItem
    const mockItems = [
      {
        _id: 'item1',
        product: {
          productId: 'prod1',
          name: 'Áo thun nam',
          sku: 'ATN123',
          price: 150000,
          cost: 100000
        },
        quantityReceived: 50,
        price: 150000,
        subTotal: 7500000,
        total: 7500000,
        inventoryTransactionId: 'inv1'
      },
      {
        _id: 'item2',
        product: {
          productId: 'prod2',
          name: 'Quần jean nữ',
          sku: 'QJN456',
          price: 320000,
          cost: 200000
        },
        quantityReceived: 30,
        price: 320000,
        subTotal: 9600000,
        total: 9600000,
        inventoryTransactionId: 'inv2'
      },
      {
        _id: 'item3',
        product: {
          productId: 'prod3',
          name: 'Áo khoác nam',
          sku: 'AKN789',
          price: 450000,
          cost: 300000
        },
        quantityReceived: 20,
        price: 450000,
        subTotal: 9000000,
        total: 9000000,
        inventoryTransactionId: 'inv3'
      }
    ];

    // Tạo mock data cho RejectedItem nếu đang chỉnh sửa
    const mockRejectedItem: RejectedItem | undefined = isEdit ? {
      _id: 'item1',
      quantity: 5,
      reason: 'Hàng bị lỗi màu, không đúng với mẫu'
    } : undefined;

    const modalData: QualityCheckRejectModalData = {
      items: mockItems,
      rejectedItem: mockRejectedItem
    };

    const result = await this.responsiveModalService.open<
      QualityCheckRejectModalData,
      RejectedItem | undefined,
      QualityCheckRejectModalComponent
    >(QualityCheckRejectModalComponent, {
      data: modalData,
      width: '500px',
      maxWidth: '95vw'
    });

    if (result) {
      console.log('Quality check reject modal result:', result);
      // Tìm tên sản phẩm để hiển thị
      const selectedItem = mockItems.find(item => item._id === result._id);
      const productName = selectedItem ? selectedItem.product.name : 'Sản phẩm';
      alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} sản phẩm bị từ chối: ${productName} - Số lượng: ${result.quantity}`);
    }
  }

  /**
   * Mở SimpleNoteModal để thêm/sửa ghi chú
   * @param existingNote Ghi chú hiện tại (nếu có)
   */
  async openSimpleNoteDialog(existingNote: string = ''): Promise<void> {
    try {
      // Mở modal với ghi chú hiện tại (nếu có)
      const title = existingNote ? 'COMMON.EDIT_NOTE' : 'COMMON.ADD_NOTE';
      const result = await this.simpleNoteModalService.open(title, existingNote);

      // Xử lý kết quả
      if (result !== undefined) {
        console.log('Simple note modal result:', result);
        alert(`Ghi chú đã được ${existingNote ? 'cập nhật' : 'thêm'}: ${result}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú đơn giản:', error);
    }
  }

  /**
   * Mở NoteModal để thêm/sửa ghi chú
   */
  async openNoteModal(): Promise<void> {
    try {
      const result = await this.noteModalService.open({
        internalNote: 'Ghi chú nội bộ mẫu',
        note: 'Ghi chú công khai mẫu'
      });

      if (result) {
        console.log('Note modal result:', result);
        alert(`Ghi chú đã được cập nhật:\nNội bộ: ${result.internalNote}\nCông khai: ${result.note}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú:', error);
    }
  }

  /**
   * Mở PromotionModal để thêm/sửa khuyến mãi
   */
  async openPromotionModal(): Promise<void> {
    try {
      const result = await this.promotionModalService.open({
        totalAmount: 500000,
        discountType: 'amount',
        discountValue: 50000,
        promotionName: 'Khuyến mãi mẫu',
        finalAmount: 450000
      });

      if (result) {
        console.log('Promotion modal result:', result);
        alert(`Khuyến mãi đã được cập nhật:\nLoại: ${result.discountType}\nGiá trị: ${result.discountValue}\nTên: ${result.promotionName}\nThành tiền: ${result.finalAmount}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal khuyến mãi:', error);
    }
  }

  /**
   * Mở ConfirmModal để xác nhận hành động
   */
  async openConfirmDialog(): Promise<void> {
    try {
      const result = await this.confirmModalService.confirm({
        title: 'COMMON.CONFIRM_ACTION',
        message: 'COMMON.CONFIRM_ACTION_MESSAGE',
        confirmText: 'COMMON.YES',
        cancelText: 'COMMON.NO',
        confirmColor: 'primary'
      });

      console.log('Confirm modal result:', result);
      alert(`Kết quả xác nhận: ${result ? 'Đồng ý' : 'Hủy bỏ'}`);
    } catch (error) {
      console.error('Lỗi khi mở modal xác nhận:', error);
    }
  }

  /**
   * Mở FieldPermissionModal để thiết lập quyền truy cập field
   */
  async openFieldPermissionModal(): Promise<void> {
    try {
      const result = await this.fieldPermissionModalService.openWithMockData(
        'Customer Name', // Tên field
        5 // Số lượng profiles
      );

      if (result) {
        console.log('Field permission modal result:', result);
        const summary = result.map(p => `${p.name}: ${p.permission}`).join('\n');
        alert(`Quyền truy cập đã được cập nhật:\n${summary}`);
      } else {
        alert('Thiết lập quyền đã bị hủy!');
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thiết lập quyền field:', error);
    }
  }

  /**
   * Mở FieldPropertiesModal để chỉnh sửa thuộc tính field
   */
  async openFieldPropertiesModal(fieldType: string = 'text'): Promise<void> {
    try {
      // Tạo mock data cho CustomField
      const mockField: any = {
        _id: 1,
        label: 'Customer Name',
        type: fieldType,
        value: fieldType === 'checkbox' ? false : (fieldType === 'multi-picklist' ? [] : ''),
        isPublic: false,
        isRequired: true,
        tooltip: 'Enter customer full name',
        constraints: this.getMockConstraints(fieldType)
      };

      const modalData: FieldPropertiesData = {
        field: mockField,
        availableSearchModules: [
          { _id: 'sales_quotes', name: 'Sales Quotes' },
          { _id: 'contacts', name: 'Contacts' },
          { _id: 'transactions', name: 'Transactions' }
        ]
      };

      const result = await this.responsiveModalService.open<
        FieldPropertiesData,
        any,
        FieldPropertiesModalComponent
      >(FieldPropertiesModalComponent, {
        data: modalData,
        width: '600px',
        maxWidth: '95vw',
        maxHeight: '90vh'
      });

      if (result) {
        console.log('Field properties modal result:', result);
        alert(`Thuộc tính field đã được cập nhật:\nNhãn: ${result.field?.label}\nLoại: ${result.field?.type}\nBắt buộc: ${result.field?.isRequired ? 'Có' : 'Không'}`);
      } else {
        alert('Chỉnh sửa thuộc tính đã bị hủy!');
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chỉnh sửa thuộc tính field:', error);
    }
  }

  /**
   * Tạo mock constraints dựa trên field type
   */
  private getMockConstraints(fieldType: string): any {
    switch (fieldType) {
      case 'text':
      case 'phone':
      case 'url':
        return { maxLength: 255, unique: false };
      case 'number':
        return { maxDigits: 9 };
      case 'textarea':
        return { textType: 'small', maxLength: 2000 };
      case 'picklist':
        return {
          picklistValues: ['Option 1', 'Option 2', 'Option 3'],
          sortOrder: 'input',
          defaultValue: 'Option 1'
        };
      case 'multi-picklist':
        return {
          picklistValues: ['Red', 'Green', 'Blue', 'Yellow'],
          sortOrder: 'alphabetical',
          defaultValue: ['Red', 'Blue']
        };
      case 'search':
        return { searchModule: 'sales_quotes' };
      case 'user':
        return { userType: 'single' };
      case 'upload-file':
        return { allowMultipleFiles: false, maxFiles: 1 };
      case 'upload-image':
        return { maxImages: 3 };
      case 'currency':
        return { maxDigits: 9, decimalPlaces: 2, rounding: 'normal' };
      case 'decimal':
        return { maxDigits: 16, decimalPlaces: 2, useNumberSeparator: true };
      case 'checkbox':
        return { enableByDefault: false };
      default:
        return {};
    }
  }

  /**
   * Mở ProductFilterDialog để lọc sản phẩm
   */
  openProductFilterDialog(): void {
    // Giả định đã chọn kho với ID 'wh1'
    const warehouseId = 'wh1';

    // Trạng thái bộ lọc hiện tại (giả định đã chọn một số danh mục)
    const currentFilter = {
      category: ['cat1', 'cat3'], // Đã chọn danh mục Quần áo người lớn và Giày dép
      warehouseLocation: null // Chưa chọn vị trí kho
    };

    // Mở modal
    this.responsiveModalService.open<
      ProductFilterModalData,
      ProductFilterResult,
      ProductFilterModalComponent
    >(
      ProductFilterModalComponent,
      {
        data: {
          current: currentFilter,
          warehouseId: warehouseId
        }
      }
    ).then((result: ProductFilterResult | undefined) => {
      if (result) {
        console.log('Product filter modal result:', result);
        alert(`Đã lọc được ${result.products.length} sản phẩm`);
      }
    }).catch(error => {
      console.error('Error opening product filter modal:', error);
    });
  }

  /**
   * Mở SerialNumberDialog để quản lý số serial
   */
  openSerialNumberDialog(): void {
    // Tạo một sản phẩm mẫu
    const mockProduct: ProductListItem = mockProductList[0];

    // Tạo danh sách serial mẫu
    const mockSerials: EmbeddedProductSerial[] = [
      { _id: 'serial1', serialNumber: `${mockProduct.sku}-SN001`, status: 'in_stock' },
      { _id: 'serial2', serialNumber: `${mockProduct.sku}-SN002`, status: 'in_stock' },
      { _id: 'serial3', serialNumber: `${mockProduct.sku}-SN003`, status: 'sold' },
      { _id: 'serial4', serialNumber: `${mockProduct.sku}-SN004`, status: 'damaged' },
      { _id: 'serial5', serialNumber: `${mockProduct.sku}-SN005`, status: 'in_transit' }
    ];

    // Mở dialog
    // const dialogRef = this.dialog.open(SerialNumberDialogComponent, {
    //   width: '600px',
    //   data: {
    //     product: mockProduct,
    //     serials: mockSerials
    //   }
    // });

    // // Xử lý kết quả khi đóng dialog
    // dialogRef.afterClosed().subscribe((result: EmbeddedProductSerial[] | undefined) => {
    //   if (result) {
    //     console.log('Serial number dialog result:', result);
    //     const inStockCount = result.filter(serial =>
    //       serial.status === 'in_stock' || serial.status === 'assigned'
    //     ).length;
    //     alert(`Đã cập nhật ${result.length} số serial, số lượng thực tế: ${inStockCount}`);
    //   }
    // });
  }

  /**
   * Xử lý khi filter thay đổi
   * @param event - Filter change event
   */
  onFieldFilterChange(event: FilterChangeEvent): void {
    console.log('Field filter changed:', event);

    // Tìm field tương ứng
    // const field = this.mockFields.find(f => f._id === event.fieldId);
    // if (field) {
    //   const status = event.isActive ? 'activated' : 'deactivated';
    //   console.log(`Filter for "${field.label}" ${status}`);

    //   if (event.isActive && event.filterValue) {
    //     console.log('Filter value:', event.filterValue);
    //   }
    // }
  }

  /**
   * Xử lý khi các filters được áp dụng
   * @param activeFilters - Danh sách các filter đang active
   */
  onFiltersApplied(activeFilters: FieldFilter[]): void {
    console.log('Active filters applied:', activeFilters);

    if (activeFilters.length > 0) {
      const filterSummary = activeFilters.map(filter => {
        const operator = filter.filterValue?.operator || 'unknown';
        const value = this.getFilterValueDisplay(filter.filterValue);
        return `${filter.field.label}: ${operator}${value ? ` (${value})` : ''}`;
      }).join(', ');

      console.log(`Applied ${activeFilters.length} filter(s): ${filterSummary}`);

      // Hiển thị thông báo
      alert(`✅ Đã áp dụng ${activeFilters.length} bộ lọc:\n${filterSummary}`);
    } else {
      console.log('No filters applied');
    }
  }

  /**
   * Xử lý khi filters được reset
   */
  onFiltersReset(): void {
    console.log('Filters have been reset');
    alert('🔄 Đã đặt lại tất cả bộ lọc');
  }

  /**
   * Helper method để hiển thị giá trị filter
   * @param filterValue - Giá trị filter
   * @returns String representation của giá trị
   */
  private getFilterValueDisplay(filterValue: any): string {
    if (!filterValue) return '';

    if (filterValue.value !== undefined) {
      return String(filterValue.value);
    }

    if (filterValue.values && Array.isArray(filterValue.values)) {
      return filterValue.values.join(', ');
    }

    if (filterValue.minValue !== undefined && filterValue.maxValue !== undefined) {
      return `${filterValue.minValue} - ${filterValue.maxValue}`;
    }

    if (filterValue.timeValue !== undefined && filterValue.timeUnit) {
      return `${filterValue.timeValue} ${filterValue.timeUnit}`;
    }

    return '';
  }

  // ==================== STANDARD DIALOG TESTS ====================

  /**
   * Test StandardDialog với cấu hình cơ bản - API mới
   */
  async openBasicStandardDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Cơ Bản - API Mới',
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          message: 'Đây là nội dung dialog cơ bản với API mới',
          testData: { timestamp: new Date(), version: '2.0' }
        }
      });

      if (result) {
        console.log('Basic dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action} - Data: ${JSON.stringify(testResult.data)}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở basic dialog:', error);
    }
  }

  /**
   * Test StandardDialog với confirm dialog - Tạm thời disable
   */
  async openConfirmStandardDialog(): Promise<void> {
    try {
      // TODO: Implement sau khi có SimpleConfirmComponent
      alert('Confirm dialog chưa được implement với API mới');
    } catch (error) {
      console.error('Lỗi khi mở confirm dialog:', error);
    }
  }

  /**
   * Test StandardDialog với alert dialog - Tạm thời disable
   */
  async openAlertStandardDialog(): Promise<void> {
    try {
      // TODO: Implement sau khi có SimpleAlertComponent
      alert('Alert dialog chưa được implement với API mới');
    } catch (error) {
      console.error('Lỗi khi mở alert dialog:', error);
    }
  }

  /**
   * Test StandardDialog với custom title template - API mới
   */
  async openCustomTitleDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: this.customTitleTemplate,
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          userName: 'Nguyễn Văn A',
          action: 'xóa tài khoản',
          message: 'Dialog với custom title template'
        }
      });

      if (result) {
        console.log('Custom title dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Dialog với title tùy chỉnh: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở custom title dialog:', error);
    }
  }

  /**
   * Test StandardDialog với custom actions template - API mới
   */
  async openCustomActionsDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Với Actions Tùy Chỉnh',
        actions: {
          useDefault: false,
          customActionsTemplate: this.customActionsTemplate
        },
        enableClose: true,
        data: {
          options: ['Lưu', 'Lưu và Tiếp tục', 'Hủy'],
          message: 'Dialog với custom actions template'
        }
      });

      if (result) {
        console.log('Custom actions dialog result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'custom' && testResult.customAction) {
          alert(`Hành động tùy chỉnh: ${testResult.customAction.name}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      }
    } catch (error) {
      console.error('Lỗi khi mở custom actions dialog:', error);
    }
  }

  /**
   * Test StandardDialog không cho phép đóng - API mới
   */
  async openNoCloseDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Không Thể Đóng',
        actions: {
          useDefault: true
        },
        enableClose: false,
        disableClose: true,
        data: {
          message: 'Dialog này chỉ có thể đóng bằng nút Hủy hoặc Xác nhận',
          testFeature: 'No close button'
        }
      });

      if (result) {
        console.log('No close dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở no close dialog:', error);
    }
  }

  /**
   * Test responsive dialog với width tùy chỉnh - API mới
   */
  async openResponsiveDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Responsive',
        actions: {
          useDefault: true
        },
        enableClose: true,
        width: '800px',
        data: {
          message: 'Dialog này có width cố định 800px và sẽ responsive trên mobile',
          content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
          testFeature: 'Responsive width'
        }
      });

      if (result) {
        console.log('Responsive dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Dialog responsive: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở responsive dialog:', error);
    }
  }

  /**
   * Handler cho custom actions trong template
   */
  onCustomSave(onCustomAction: Function): void {
    onCustomAction('save', { timestamp: new Date() });
  }

  onCustomSaveAndContinue(onCustomAction: Function): void {
    onCustomAction('save_continue', { timestamp: new Date(), continue: true });
  }

  onCustomCancel(onClose: Function): void {
    onClose();
  }

  // ==================== STANDARD BOTTOM SHEET TESTS ====================

  /**
   * Test StandardBottomSheet với cấu hình cơ bản - API mới
   */
  async openBasicStandardBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Cơ Bản - API Mới',
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          message: 'Đây là nội dung bottom sheet cơ bản với API mới',
          testData: { timestamp: new Date(), version: '2.0', type: 'bottom-sheet' }
        }
      });

      if (result) {
        console.log('Basic bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action} - Data: ${JSON.stringify(testResult.data)}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở basic bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet với custom title template - API mới
   */
  async openCustomTitleBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: this.customTitleTemplate,
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          userName: 'Nguyễn Văn B',
          action: 'xem chi tiết sản phẩm',
          message: 'Bottom sheet với custom title template'
        }
      });

      if (result) {
        console.log('Custom title bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Bottom sheet với title tùy chỉnh: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở custom title bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet với custom actions template - API mới
   */
  async openCustomActionsBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Với Actions Tùy Chỉnh',
        actions: {
          useDefault: false,
          customActionsTemplate: this.customActionsTemplate
        },
        enableClose: true,
        data: {
          options: ['Lưu', 'Lưu và Tiếp tục', 'Hủy'],
          message: 'Bottom sheet với custom actions template'
        }
      });

      if (result) {
        console.log('Custom actions bottom sheet result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'custom' && testResult.customAction) {
          alert(`Hành động tùy chỉnh: ${testResult.customAction.name}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      }
    } catch (error) {
      console.error('Lỗi khi mở custom actions bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet không cho phép đóng - API mới
   */
  async openNoCloseBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Không Thể Đóng',
        actions: {
          useDefault: true
        },
        enableClose: false,
        disableClose: true,
        data: {
          message: 'Bottom sheet này chỉ có thể đóng bằng nút Hủy hoặc Xác nhận',
          testFeature: 'No close button'
        }
      });

      if (result) {
        console.log('No close bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở no close bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet với backdrop disabled
   */
  async openNoBackdropBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Không Có Backdrop',
        actions: {
          useDefault: true
        },
        enableClose: true,
        hasBackdrop: false,
        data: {
          message: 'Bottom sheet này không có backdrop, không thể đóng bằng click outside',
          testFeature: 'No backdrop'
        }
      });

      if (result) {
        console.log('No backdrop bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Bottom sheet không backdrop: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở no backdrop bottom sheet:', error);
    }
  }

  // ===== CreateLayoutModal Tests =====

  /**
   * Test CreateLayoutModal với ResponsiveModalService
   */
  async openCreateLayoutModal(): Promise<void> {
    try {
      const existingLayouts = ['Fashion Layout', 'Electronics Layout', 'Food Layout'];

      const result = await this.createLayoutModalService.openWithValidation(
        existingLayouts,
        'New Layout', // default name
        'Layout được tạo từ test' // default description
      );

      if (result) {
        console.log('CreateLayoutModal result:', result);
        this.flashMessageService.success(
          `Layout "${result.name}" đã được tạo thành công!`
        );
      } else {
        console.log('CreateLayoutModal cancelled');
        this.flashMessageService.info('Hủy tạo layout');
      }
    } catch (error) {
      console.error('Lỗi khi mở CreateLayoutModal:', error);
      this.flashMessageService.error('Có lỗi xảy ra khi mở modal tạo layout');
    }
  }

  /**
   * Test CreateLayoutModal không có validation
   */
  async openCreateLayoutModalSimple(): Promise<void> {
    try {
      const result = await this.createLayoutModalService.open();

      if (result) {
        console.log('CreateLayoutModal simple result:', result);
        this.flashMessageService.success(
          `Layout "${result.name}" đã được tạo thành công!`
        );
      } else {
        console.log('CreateLayoutModal simple cancelled');
        this.flashMessageService.info('Hủy tạo layout');
      }
    } catch (error) {
      console.error('Lỗi khi mở CreateLayoutModal simple:', error);
      this.flashMessageService.error('Có lỗi xảy ra khi mở modal tạo layout');
    }
  }

  // ===== ResponsiveModalService Tests =====

  /**
   * Test ResponsiveModalService với auto-selection
   */
  async openResponsiveModal(): Promise<void> {
    try {
      const modalConfig: ResponsiveModalConfig<{ message: string }> = {
        title: 'Responsive Modal Test',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: { message: 'Test ResponsiveModalService với auto-selection' }
      };

      const result = await this.responsiveModalService.open(SimpleBottomSheetTestComponent, modalConfig);

      if (result) {
        console.log('Responsive modal result:', result);
        const testResult = result as TestModalResult;
        alert(`Responsive modal result: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở responsive modal:', error);
    }
  }

  /**
   * Test ResponsiveModalService force dialog mode
   */
  async openForceDialogModal(): Promise<void> {
    try {
      const result = await this.responsiveModalService.open<
        SimpleBottomSheetTestData,
        SimpleBottomSheetTestResult,
        SimpleBottomSheetTestComponent
      >(
        SimpleBottomSheetTestComponent,
        {
          title: 'Force Dialog Mode',
          enableClose: true,
          actions: {
            useDefault: true
          },
          data: { message: 'Test force dialog mode', testData: { id: 2, name: 'Dialog Test' } },
          width: '500px'
        },
        'dialog' // Force dialog mode
      );

      if (result) {
        console.log('Force dialog modal result:', result);
        alert(`Force dialog modal result: ${result.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở force dialog modal:', error);
    }
  }

  /**
   * Test ResponsiveModalService force bottom sheet mode
   */
  async openForceBottomSheetModal(): Promise<void> {
    try {
      const modalConfig: ResponsiveModalConfig<{ message: string }> = {
        title: 'Force Bottom Sheet Mode',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: { message: 'Test force bottom sheet mode' }
      };

      const result = await this.responsiveModalService.open(
        SimpleBottomSheetTestComponent,
        modalConfig,
        'bottom-sheet' // Force bottom sheet mode
      );

      if (result) {
        console.log('Force bottom sheet modal result:', result);
        const testResult = result as TestModalResult;
        alert(`Force bottom sheet modal result: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở force bottom sheet modal:', error);
    }
  }

  /**
   * Test ListColumnSelectorModal với force bottom sheet mode
   */
  async openListColumnSelectorBottomSheet(): Promise<void> {
    try {
      // Tạo mock data cho ListColumnConfig theo interface đúng
      const allColumns = [
        { _id: 'id', label: 'ID', sortable: true },
        { _id: 'name', label: 'Tên sản phẩm', sortable: true },
        { _id: 'price', label: 'Giá', sortable: true },
        { _id: 'category', label: 'Danh mục', sortable: true },
        { _id: 'brand', label: 'Thương hiệu', sortable: true },
        { _id: 'stock', label: 'Tồn kho', sortable: true },
        { _id: 'status', label: 'Trạng thái', sortable: false }
      ];

      const regularColumns = [
        { _id: 'id', label: 'ID', sortable: true },
        { _id: 'name', label: 'Tên sản phẩm', sortable: true },
        { _id: 'price', label: 'Giá', sortable: true },
        { _id: 'stock', label: 'Tồn kho', sortable: true }
      ];

      const pinnedColumns = [
        { _id: 'status', label: 'Trạng thái', sortable: false }
      ];

      const mockColumnConfig: ListColumnConfig = {
        allColumns,
        regularColumns,
        pinnedColumns,
        maxPinnedColumns: 3
      };

      console.log('Opening ListColumnSelector with bottom sheet mode...');

      const modalConfig: ResponsiveModalConfig<ListColumnConfig> = {
        title: 'Chọn cột hiển thị - Bottom Sheet',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: mockColumnConfig
      };

      const result = await this.responsiveModalService.open(
        ListColumnSelectorModalComponent,
        modalConfig,
        'bottom-sheet' // Force bottom sheet mode để test
      );

      if (result) {
        console.log('ListColumnSelector bottom sheet result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'confirm' && testResult.data) {
          const columnData = testResult.data as { regularColumns: { label: string }[]; pinnedColumns: { label: string }[] };
          const totalColumns = columnData.regularColumns.length + columnData.pinnedColumns.length;
          alert(`✅ Đã cập nhật cột hiển thị!\nSố cột hiển thị: ${totalColumns}\nCột thường: ${columnData.regularColumns.map(col => col.label).join(', ')}\nCột ghim: ${columnData.pinnedColumns.map(col => col.label).join(', ')}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      } else {
        alert('❌ Đã hủy chọn cột');
      }
    } catch (error) {
      console.error('Lỗi khi mở ListColumnSelector bottom sheet:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test ValidationTestComponent với AdvancedModalComponent interface
   */
  async openValidationTestModal(): Promise<void> {
    try {
      // Mock data ban đầu
      const initialData: ValidationTestData = {
        name: 'John',
        email: '', // Email trống để test validation
        agreeToTerms: false
      };

      console.log('Opening ValidationTest modal...');

      const modalConfig: ResponsiveModalConfig<ValidationTestData> = {
        title: '🧪 Test Validation Modal',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: initialData
      };

      const result = await this.responsiveModalService.open(
        ValidationTestComponent,
        modalConfig
      );

      if (result) {
        console.log('ValidationTest modal result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'confirm' && testResult.data) {
          const data = testResult.data as ValidationTestResult;
          alert(`✅ Form đã được submit thành công!\n\nThông tin:\n- Tên: ${data.name}\n- Email: ${data.email}\n- Đồng ý điều khoản: ${data.agreeToTerms ? 'Có' : 'Không'}\n- Thời gian submit: ${data.submittedAt.toLocaleString()}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      } else {
        alert('❌ Đã hủy form');
      }
    } catch (error) {
      console.error('Lỗi khi mở ValidationTest modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test StrictTestModalComponent với type safety đầy đủ
   */
  async openStrictTestModal(): Promise<void> {
    try {
      // Mock data ban đầu
      const initialData: StrictTestModalData = {
        userName: 'John Doe',
        email: '<EMAIL>',
        message: 'Test StrictModalComponent interface với type safety đầy đủ'
      };

      console.log('Opening StrictTest modal...');

      const result = await this.responsiveModalService.open<
        StrictTestModalData,
        StrictTestModalResult,
        StrictTestModalComponent
      >(
        StrictTestModalComponent,
        {
          title: '🧪 Strict Test Modal (Type Safe)',
          enableClose: true,
          actions: {
            useDefault: true
          },
          data: initialData
        }
      );

      if (result) {
        console.log('StrictTest modal result:', result);
        alert(`✅ Form đã được submit thành công với type safety!\n\nThông tin:\n- Tên: ${result.userName}\n- Email: ${result.email}\n- Đồng ý điều khoản: ${result.termsAccepted ? 'Có' : 'Không'}\n- Thời gian submit: ${result.timestamp.toLocaleString()}`);
      } else {
        alert('❌ Đã hủy form');
      }
    } catch (error) {
      console.error('Lỗi khi mở StrictTest modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test ValidationTestComponent với force bottom sheet mode
   */
  async openValidationTestBottomSheet(): Promise<void> {
    try {
      // Mock data ban đầu với một số field đã điền
      const initialData: ValidationTestData = {
        name: 'Jane Doe',
        email: '<EMAIL>',
        agreeToTerms: false // Chưa đồng ý để test validation
      };

      console.log('Opening ValidationTest bottom sheet...');

      const modalConfig: ResponsiveModalConfig<ValidationTestData> = {
        title: '🧪 Test Validation Bottom Sheet',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: initialData
      };

      const result = await this.responsiveModalService.open(
        ValidationTestComponent,
        modalConfig,
        'bottom-sheet' // Force bottom sheet mode
      );

      if (result) {
        console.log('ValidationTest bottom sheet result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'confirm' && testResult.data) {
          const data = testResult.data as ValidationTestResult;
          alert(`✅ Form đã được submit thành công từ Bottom Sheet!\n\nThông tin:\n- Tên: ${data.name}\n- Email: ${data.email}\n- Đồng ý điều khoản: ${data.agreeToTerms ? 'Có' : 'Không'}\n- Thời gian submit: ${data.submittedAt.toLocaleString()}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      } else {
        alert('❌ Đã hủy form');
      }
    } catch (error) {
      console.error('Lỗi khi mở ValidationTest bottom sheet:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test IntegrationAccountViewModalComponent
   */
  async openIntegrationAccountViewModal(): Promise<void> {
    try {
      // Tạo mock settings config
      const settingsConfig: SettingsListConfig = {
        settings: mockAllSettings.slice(0, 6), // Lấy 6 settings đầu tiên
        values: {
          ...mockDefaultSettingValues,
          notifications_enabled: true,
          auto_save: false,
          dark_mode: true
        },
        showDividers: true,
        showCard: false,
        autoSave: false, // Để user phải click Save
        showSaveButtons: true
      };

      // Lấy mock logs cho ShopeeFood
      const logs = getMockLogsByPlatform('shopeefood');

      // Chuẩn bị dữ liệu cho modal
      const modalData: IntegrationAccountViewModalData = {
        config: settingsConfig,
        logs: logs,
        platformName: 'Shopee Food',
        accountName: 'Test Account'
      };

      console.log('Opening Integration Account View modal...');

      const result = await this.responsiveModalService.open<
        IntegrationAccountViewModalData,
        IntegrationAccountViewModalResult
      >(
        IntegrationAccountViewModalComponent,
        {
          title: 'INTEGRATION_ACCOUNT_VIEW_TITLE',
          data: modalData,
          width: '800px',
          maxWidth: '95vw',
          disableClose: false
        }
      );

      if (result) {
        console.log('Integration Account View modal result:', result);
        if (result.settingsChanged) {
          alert(`✅ Settings đã được thay đổi!\n\nAction: ${result.action}\nChanged Settings: ${JSON.stringify(result.changedSettings, null, 2)}`);
        } else {
          alert(`ℹ️ Modal đã đóng mà không có thay đổi.\n\nAction: ${result.action}`);
        }
      } else {
        alert('❌ Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở Integration Account View modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test IntegrationAccountSettingsModalComponent
   */
  async openIntegrationAccountSettingsModal(): Promise<void> {
    try {
      // Tạo mock settings config
      const settingsConfig: SettingsListConfig = {
        settings: mockAllSettings.slice(0, 6), // Lấy 6 settings đầu tiên
        values: {
          ...mockDefaultSettingValues,
          notifications_enabled: true,
          auto_save: false,
          dark_mode: true
        },
        showDividers: true,
        showCard: false,
        autoSave: false, // Để user phải click Save
        showSaveButtons: true
      };

      // Chuẩn bị dữ liệu cho modal
      const modalData: IntegrationAccountSettingsModalData = {
        config: settingsConfig,
        platformName: 'Shopee Food',
        accountName: 'Test Settings Account'
      };

      console.log('Opening Integration Account Settings modal...');

      const result = await this.responsiveModalService.open<
        IntegrationAccountSettingsModalData,
        IntegrationAccountSettingsModalResult
      >(
        IntegrationAccountSettingsModalComponent,
        {
          title: 'INTEGRATION_ACCOUNT_SETTINGS_TITLE',
          data: modalData,
          width: '700px',
          maxWidth: '95vw',
          disableClose: false
        }
      );

      if (result) {
        console.log('Integration Account Settings modal result:', result);
        if (result.settingsChanged) {
          alert(`✅ Settings đã được thay đổi!\n\nAction: ${result.action}\nChanged Settings: ${JSON.stringify(result.changedSettings, null, 2)}`);
        } else {
          alert(`ℹ️ Modal đã đóng mà không có thay đổi.\n\nAction: ${result.action}`);
        }
      } else {
        alert('❌ Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở Integration Account Settings modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test IntegrationAccountLogsModalComponent
   */
  async openIntegrationAccountLogsModal(): Promise<void> {
    try {
      // Lấy mock logs cho ShopeeFood
      const logs = getMockLogsByPlatform('shopeefood');

      // Chuẩn bị dữ liệu cho modal
      const modalData: IntegrationAccountLogsModalData = {
        logs: logs,
        platformName: 'Shopee Food',
        accountName: 'Test Logs Account'
      };

      console.log('Opening Integration Account Logs modal...');

      const result = await this.responsiveModalService.open<
        IntegrationAccountLogsModalData,
        IntegrationAccountLogsModalResult
      >(
        IntegrationAccountLogsModalComponent,
        {
          title: 'INTEGRATION_ACCOUNT_LOGS_TITLE',
          data: modalData,
          width: '800px',
          maxWidth: '95vw',
          disableClose: false
        }
      );

      if (result) {
        console.log('Integration Account Logs modal result:', result);
        alert(`ℹ️ Logs modal đã đóng.\n\nAction: ${result.action}\nTotal Logs: ${logs.length}`);
      } else {
        alert('❌ Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở Integration Account Logs modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }
}
