import { Field, FieldPermissionProfile } from '@domain/entities/field.entity';
import { FieldPropertiesData } from '../modals/field-properties/field-properties-modal.component';


export interface Section {
  _id: string; // ID duy nhất của section
  id?: string; // Backward compatibility
  title: string; // Tiêu đề section (ví dụ: "Thông tin cơ bản")
  fields: Field[]; // Danh sách fields trong section
  settings: {
    // default 'double-column'
    sectionLayoutColumn: 'single-column' | 'double-column';
  },
}

// Widget configuration interface cho Detail View
export interface WidgetConfig {
  _id: string;
  name: string;
  description: string;
}

// Detail View section interface
export interface DetailViewSection {
  _id: string;
  name: string;
  widgets: WidgetConfig[];
}

// Quick Create configuration interface - CHANGED: sections -> section (singular)
export interface QuickCreateConfig {
  title: string;
  description?: string;
  section: Section; // CHANGED: singular instead of array
  enableAutoSave?: boolean;
}

// Detail View configuration interface
export interface DetailViewConfig {
  title: string;
  description?: string;
  sections: DetailViewSection[];
}


/**
 * Sử dụng Input trong Component:
    layoutConfig: Khởi tạo Golden Layout với bố cục panels.
    sections: Hiển thị sections và fields trong Golden Layout.
    templates: Hiển thị trong dropdown chọn template ngành.
    tenantId: Gửi kèm trong API call và kiểm tra quyền qua IAM.
    availableFieldTypes: Hiển thị danh sách Field Types trong block New Fields.

    Validation Input:
    layoutConfig: Nếu cung cấp, phải hợp lệ theo cấu trúc Golden Layout. Nếu không, sử dụng mặc định.
    sections: Mảng có thể rỗng, nhưng mỗi section phải có id duy nhất và fields hợp lệ.
    templates: Mảng có thể rỗng, nhưng mỗi template phải có name và sections hợp lệ.
    availableFieldTypes: Phải chứa ít nhất một loại field, với type và label hợp lệ.
    Nếu input không hợp lệ, component hiển thị thông báo lỗi (dùng mat-snack-bar) và fallback về trạng thái mặc định.
 */
export interface DynamicLayoutConfigDto {
  /** ID duy nhất của layout (cho multi-layout mode) */
  _id?: string;

  // Dữ liệu sections - danh sách các section hiện có
  /** Danh sách các section ban đầu để hiển thị trong layout */
  sections: Section[];
  /** Cấu hình Quick Create tab */
  quickCreateConfig: QuickCreateConfig;
  /** Cấu hình Detail View tab */
  detailViewConfig: DetailViewConfig;

  /**
   * Layout mặc định sẽ được lấy từ layout có isDefault = true hoặc layouts[0]
   */
  isDefault?: boolean;

  fieldDefaultSettings: {
    permissionProfiles: FieldPermissionProfile[],
    availableSearchModules: FieldPropertiesData['availableSearchModules']
  },

  /** Thời gian tạo */
  createdAt: Date;
  /** Thời gian cập nhật cuối */
  updatedAt: Date;
}
