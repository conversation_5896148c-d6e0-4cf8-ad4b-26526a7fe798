// Removed unused imports: signal, BehaviorSubject

import { Section } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.dto';
import { Field } from '@domain/entities/field.entity';
import { CoreLayoutBuilderService } from '../../../../services/core-layout-builder.service';
import { DynamicLayoutConfigStateService } from '../../../../services/dynamic-layout-config-state.service';
import { StateChangeEventPayload, StateChangeEventType } from '../../../../models/dynamic-layout-builder.model';
import { DynamicLayoutBuilderStateService } from '../../../../services/dynamic-layout-builder-state.service';
import { inject } from '@angular/core';
import { FlashMessageService } from '@/core/services/flash_message.service';
import { TranslateService } from '@ngx-translate/core';
import { getFieldTypeLabel } from '../../../../constants/field-types.const';

/**
 * CreateTabService - Non-injectable service xử lý logic nghiệp vụ cho Create tab
 *
 * ✅ REFACTORED: Removed @Injectable() decorator - instantiate with 'new CreateTabService()'
 * ✅ ADDED: Nhận CoreLayoutBuilderService qua constructor để sử dụng instance đã inject
 *
 * Responsibilities:
 * - Section management: Tạo, xóa, cập nhật sections
 * - Field management: Thêm, xóa, sắp xếp fields
 * - Business logic: Validation, transformation, state management
 * - Integration: Sử dụng CoreLayoutBuilderService cho core operations
 *
 * Usage:
 * - Instantiate trong component: `private createTabService = new CreateTabService(this.coreLayoutBuilderService);`
 * - Sử dụng instance đã inject để tránh dependency injection issues
 */
export class CreateTabService {
  private coreLayoutBuilderService = new CoreLayoutBuilderService();

  /**
   * Constructor nhận CoreLayoutBuilderService đã inject
   */
  constructor(
    private configStateService: DynamicLayoutConfigStateService,
    private stateService: DynamicLayoutBuilderStateService,
    private translateService: TranslateService,
    private flashMessageService: FlashMessageService
  ) {}
  // ==================== SECTION MANAGEMENT BUSINESS LOGIC ====================

  /**
   * Tạo section mới
   * Business Logic: Sử dụng CoreLayoutBuilderService để tạo section với ID unique
   */
  createNewSection() {
    const updatedSections = [
      ...this.configStateService.getCreateTabSections(), 
      this.coreLayoutBuilderService.createDefaultSection()
    ];
    return this.updateSections(
      updatedSections,
      StateChangeEventType.SECTION_ADDED,
      updatedSections
    );
  }

  /**
   * Xóa section
   * Business Logic: Sử dụng CoreLayoutBuilderService để xóa section
   */
  deleteSection(section: Section) {
    return this.updateSections(
      this.coreLayoutBuilderService.handleSectionDeletion(
        this.configStateService.getCreateTabSections(),
        section._id
      ),
      StateChangeEventType.SECTION_DELETED,
      section
    );
  }

  /**
   * Cập nhật tiêu đề section
   * Business Logic: Sử dụng CoreLayoutBuilderService để cập nhật tiêu đề section
   */
  updateSectionTitle(section: Section, newTitle: string) {
    return this.updateSections(
      this.coreLayoutBuilderService.handleSectionTitleChange(
        this.configStateService.getCreateTabSections(),
        section._id,
        newTitle
      ),
      StateChangeEventType.SECTION_TITLE_CHANGED,
      section
    );
  }

  // ==================== FIELD MANAGEMENT BUSINESS LOGIC ====================
  /**
   * Xóa field khỏi section
   * Business Logic: Sử dụng CoreLayoutBuilderService để xóa field khỏi section
   * ✅ FIXED: Support both string and number fieldId for backward compatibility
   */
  deleteFieldFromSection(data: { section: Section; field: Field }): Section[] {
    return this.updateSections(
      this.coreLayoutBuilderService.removeFieldFromSection(
        this.configStateService.getCreateTabSections(), 
        data.section._id, 
        data.field._id!
      ),
      StateChangeEventType.FIELD_DELETED,
      data.field
    );
  }

  /**
   * Cập nhật thứ tự fields trong section
   * Business Logic: Sử dụng CoreLayoutBuilderService để sắp xếp lại fields
   */
  reorderFieldsInSection(section: Section, reorderedFields: Field[]) {
    return this.updateSections(
      this.coreLayoutBuilderService.handleFieldReorderBusinessLogic(
        this.configStateService.getCreateTabSections(),
        section._id,
        reorderedFields
      ),
      StateChangeEventType.FIELD_REORDERED, 
      reorderedFields
    );
  }

  quickAddFieldToSection(data: { section: Section; fieldType: string }) {
    // Lấy defaultPermissionProfiles từ layout config
    const defaultPermissionProfiles = this.configStateService.currentLayout()?.fieldDefaultSettings?.permissionProfiles || [];

    const newField = this.coreLayoutBuilderService.createFieldFromType(
      data.fieldType,
      data.section.fields.length + 1,
      defaultPermissionProfiles
    );

    if (!newField) {
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERROR.GENERAL.CREATION_FAILED')
      );
      return;
    }

    // Dịch label từ translation key sang text tiếng Việt
    newField.label = getFieldTypeLabel(newField.type, this.translateService);

    return this.updateSections(
      this.coreLayoutBuilderService.addFieldToSection(
        this.configStateService.getCreateTabSections(), 
        data.section._id || data.section.id || '', 
        newField
      )
    );
  }


  /**
   * Toggle trạng thái required của field
   * Business Logic: Sử dụng CoreLayoutBuilderService để cập nhật trạng thái required
   */
  toggleFieldRequired(data: { section: Section; field: Field; isRequired: boolean }) {
    return this.updateSections(this.coreLayoutBuilderService.updateFieldInSection(
      this.configStateService.getCreateTabSections(),
      data.section._id,
      data.field._id!,
      { required: data.isRequired }
    ));
  }

  /**
   * Cập nhật properties của field
   * Business Logic: Sử dụng CoreLayoutBuilderService để cập nhật properties của field
   */
  updateFieldProperties(data: { section: Section; field: Field }) {
    return this.updateSections(
      this.coreLayoutBuilderService.updateFieldInSection(
        this.configStateService.getCreateTabSections(),
        data.section._id,
        data.field._id!,
        data.field
      ), 
      StateChangeEventType.FIELD_PROPERTIES_EDITED, 
      data.field
    );
  }

  /**
   * Cập nhật permissions của field
   * Business Logic: Sử dụng CoreLayoutBuilderService để cập nhật permissions của field
   * ✅ FIXED: Support both string and number fieldId for backward compatibility
   */
  updateFieldPermissions(data: { section: Section; field: Field }): Section[] {
    return this.updateSections(this.coreLayoutBuilderService.updateFieldInSection(
      this.configStateService.getCreateTabSections(),
      data.section._id,
      data.field._id!,
      {
        constraints: {
          ...data.field.constraints,
          permissions: (data.field.constraints as any)?.permissions
        }
      }
    ));
  }


  private updateSections(updatedSections: Section[], eventType?: StateChangeEventType, payload?: StateChangeEventPayload) {
    this.configStateService.updateCreateTabData(updatedSections);

    if(eventType) {
      this.stateService.trackEvent(eventType, payload);
    }

    this.configStateService.setUnsavedChanges(true);
    this.triggerAutoSave();
    
    return updatedSections;
  }

  triggerAutoSave(): void {
    // const currentSections = this.configStateService.getCreateTabSections();

    // // ✅ NEW: Check autoSave config trước khi auto-save
    // const currentState = this.configStateService.getCurrentState();
    // const autoSaveEnabled = currentState?.currentLayout?.autoSave ?? true;

    // if (!autoSaveEnabled) {
    //   // console.log('⏸️ CreateTab: Auto-save disabled, skipping auto-save');
    //   return;
    // }

    // // console.log('💾 CreateTab: Triggering auto-save với', currentSections.length, 'sections');

    // // Cập nhật data trong DynamicLayoutConfigStateService
    // this.configStateService.updateCreateTabData(currentSections);
  }
}
