import { Component, Input, signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {  PreviewData } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { PreviewPanelService, ValidationResult } from './preview-panel.service';
import { Section } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.dto';
import { Field } from '@domain/entities/field.entity';

/**
 * Component hiển thị preview dữ liệu khách hàng dựa trên layout đã tạo
 * Cho phép xem trước giao diện form và test nhập liệu
 */
@Component({
  selector: 'app-preview-panel',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRadioModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTabsModule,
    TranslateModule
  ],
  templateUrl: './preview-panel.component.html',
  styleUrls: ['./preview-panel.component.scss']
})
export class PreviewPanelComponent {
  private previewPanelService = inject(PreviewPanelService);

  /**
   * Danh sách sections để preview
   */
  @Input() sections = signal<Section[]>([]);

  /**
   * Signal cho dữ liệu preview
   */
  previewData = signal<PreviewData>({});

  /**
   * Signal cho kết quả validation
   */
  validationResults = signal<ValidationResult[]>([]);

  /**
   * Computed để kiểm tra có fields nào không
   */
  hasAnyFields = computed(() => {
    return this.sections().some(section => section.fields.length > 0);
  });

  /**
   * Computed để kiểm tra có dữ liệu nào không
   */
  hasAnyData = computed(() => {
    return Object.keys(this.previewData()).length > 0;
  });

  /**
   * Computed để kiểm tra có lỗi validation không
   */
  hasValidationErrors = computed(() => {
    return this.validationResults().some(result => result.type === 'error');
  });

  /**
   * Điền dữ liệu mẫu
   */
  onFillSampleData(): void {
    const sampleData = this.previewPanelService.generateSampleData(this.sections());
    this.previewData.set(sampleData);
    console.log('Sample data filled:', sampleData);
  }

  /**
   * Xóa tất cả dữ liệu
   */
  onClearData(): void {
    this.previewData.set({});
    this.validationResults.set([]);
  }

  /**
   * Validate form
   */
  onValidateForm(): void {
    const results = this.previewPanelService.validateFormData(this.sections(), this.previewData());
    this.validationResults.set(results);
  }

  /**
   * Lấy key cho field
   */
  getFieldKey(field: Field): string {
    return this.previewPanelService.getFieldKey(field);
  }

  /**
   * Lấy options cho select field
   */
  getSelectOptions(field: Field): Array<{ value: string; label: string }> {
    return this.previewPanelService.getSelectOptions(field);
  }

  /**
   * Lấy options cho radio field
   */
  getRadioOptions(field: Field): Array<{ value: string; label: string }> {
    return this.previewPanelService.getRadioOptions(field);
  }

  /**
   * Lấy JSON của form data
   */
  getFormDataJson(): string {
    return this.previewPanelService.formatFormDataAsJson(this.previewData());
  }

  /**
   * TrackBy function cho sections
   */
  trackBySection(_index: number, section: Section): string {
    return section._id || "";
  }

  /**
   * TrackBy function cho fields
   */
  trackByField(_index: number, field: Field): string | number {
    return field._id || _index;
  }
}
