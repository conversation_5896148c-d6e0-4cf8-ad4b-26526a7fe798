import { Observable, of } from 'rxjs';
import { delay, tap } from 'rxjs/operators';

// Import interface mới từ dynamic-layout-config.model.ts
import { DynamicLayoutConfig } from '../models/dynamic-layout-config.model';
// Import các interface kh<PERSON>c từ model cũ (giữ lại để tham khảo)
import { Template, SubmitLayoutResponse } from '../models/dynamic-layout-builder.model';
import { Section } from '../models/dynamic-layout-config.dto';
import { Field, FieldType, FieldPermissionProfile } from '@domain/entities/field.entity';
// Import constants và types mới
import { generateTempFieldId, getFieldTypeById, DEFAULT_FIELD_TYPES } from '../constants/field-types.const';

/**
 * CoreLayoutBuilderService
 *
 * ✅ REFACTORED: Stateless utility service for core layout building operations
 * ✅ CHANGED: Removed all state management - components manage their own state
 * ✅ CHANGED: All methods now accept sections parameter instead of using internal state
 *
 * Responsibilities:
 * - Field Management: All field-related operations (field types, field validation, field manipulation)
 * - Section Management: All section-related operations (section CRUD, section validation, section ordering)
 * - Template Management: All template-related operations (template loading, template processing, template validation)
 * - Layout Operations: All layout building operations (layout creation, layout validation, layout processing)
 * - Storage Operations: Core layout persistence methods (saveLayout, loadLayout, etc.)
 * - Utility Methods: Helper methods for layout building operations
 *
 * Architecture:
 * - Stateless utility service với pure methods
 * - No internal state management - components handle their own state
 * - All methods work with passed-in sections parameter
 * - Pure functions with no side effects
 * - Error handling và logging
 *
 * Usage:
 * - Instantiate trong component: `private coreService = new CoreLayoutBuilderService();`
 * - Pass sections array to all methods that need it
 * - Components manage their own sections state
 */
export class CoreLayoutBuilderService {
  /**
   * Tạo section mặc định
   * ✅ REFACTORED: Pure function - no state management
   */
  createDefaultSection(): Section {
    const section = {
      _id: this.generateSectionId(),
      title: 'New Section',
      fields: []
    };
    return section;
  }

  /**
   * Xử lý section title change logic
   * ✅ REFACTORED: Pure function with sections parameter
   * ✅ FIXED: Support both _id and id for backward compatibility
   */
  handleSectionTitleChange(sections: Section[], sectionId: string, newTitle: string): Section[] {
    const updatedSections = [...sections];
    // ✅ FIXED: Find section by both _id and id for backward compatibility
    const index = updatedSections.findIndex(s => (s._id) === sectionId);
    if (index !== -1) {
      updatedSections[index] = { ...updatedSections[index], title: newTitle };
    } else {
      console.error('❌ CoreLayoutBuilderService: Section not found for title change:', sectionId);
    }
    return updatedSections;
  }

  /**
   * Xử lý section deletion logic
   * ✅ REFACTORED: Pure function with sections parameter
   * ✅ FIXED: Support both _id and id for backward compatibility
   */
  handleSectionDeletion(sections: Section[], sectionId: string): Section[] {
    // ✅ FIXED: Filter by both _id and id for backward compatibility
    const filteredSections = sections.filter(s => (s._id) !== sectionId);
    return filteredSections;
  }

  // ==================== FIELD MANAGEMENT ====================

  /**
   * Thêm field vào section
   * ✅ REFACTORED: Pure function with sections parameter
   */
  addFieldToSection(sections: Section[], sectionId: string, fieldType: Field): Section[] {
    // Tìm section theo cả id và _id để tương thích với MongoDB
    const sectionIndex = sections.findIndex(s =>
      s._id === sectionId || s._id === sectionId
    );

    if (sectionIndex === -1) {
      console.error('❌ CoreLayoutBuilderService: Section not found:', sectionId);
      console.log('🔍 Available sections:', sections.map(s => ({
        // id: s._id, // Removed - using _id only
        _id: s._id,
        title: s.title
      })));
      return sections;
    }

    // Tạo field mới với ID duy nhất
    const newField: Field = {
      ...fieldType,
      _id: this.generateFieldId(),
      label: fieldType.label || `New ${fieldType.type}`,
      isRequired: false,
      isPublic: true,
      order: sections[sectionIndex].fields.length + 1
    };

    // Cập nhật section với field mới
    const updatedSections = [...sections];
    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: [...updatedSections[sectionIndex].fields, newField]
    };

    console.log('✅ CoreLayoutBuilderService: Field added to section:', sectionId, newField._id);
    return updatedSections;
  }

  /**
   * Xóa field khỏi section
   * ✅ REFACTORED: Pure function with sections parameter
   */
  removeFieldFromSection(sections: Section[], sectionId: string, fieldId: string | number): Section[] {
    const sectionIndex = sections.findIndex(s => s._id === sectionId);

    if (sectionIndex === -1) {
      console.error('❌ CoreLayoutBuilderService: Section not found:', sectionId);
      return sections;
    }

    // Xóa field khỏi section
    const updatedSections = [...sections];
    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: updatedSections[sectionIndex].fields.filter(f => f._id !== fieldId)
    };

    console.log('✅ CoreLayoutBuilderService: Field removed from section:', sectionId, fieldId);
    return updatedSections;
  }

  /**
   * Cập nhật field trong section
   * ✅ REFACTORED: Pure function with sections parameter
   * ✅ FIXED: Support both _id and id for backward compatibility
   */
  updateFieldInSection(sections: Section[], sectionId: string, fieldId: string | number, updatedField: Partial<Field>): Section[] {
    // ✅ FIXED: Find section by both _id and id for backward compatibility
    const sectionIndex = sections.findIndex(s => (s._id) === sectionId);

    if (sectionIndex === -1) {
      console.error('❌ CoreLayoutBuilderService: Section not found:', sectionId);
      return sections;
    }

    const fieldIndex = sections[sectionIndex].fields.findIndex(f => f._id === fieldId);
    if (fieldIndex === -1) {
      console.error('❌ CoreLayoutBuilderService: Field not found:', fieldId, 'in section:', sectionId);
      return sections;
    }

    // Cập nhật field
    const updatedSections = [...sections];
    const currentField = updatedSections[sectionIndex].fields[fieldIndex];
    const updatedFieldData = { ...currentField, ...updatedField } as Field;

    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: [
        ...updatedSections[sectionIndex].fields.slice(0, fieldIndex),
        updatedFieldData,
        ...updatedSections[sectionIndex].fields.slice(fieldIndex + 1)
      ]
    };

    return updatedSections;
  }

  /**
   * Xử lý field reorder logic
   * ✅ REFACTORED: Pure function with sections parameter
   */
  handleFieldReorder(sections: Section[], sectionId: string, fields: Field[]): Section[] {
    const updatedSections = [...sections];
    const index = updatedSections.findIndex(s => s._id === sectionId);
    if (index !== -1) {
      updatedSections[index] = { ...updatedSections[index], fields };
      console.log('✅ CoreLayoutBuilderService: Field reorder applied to section:', sectionId, 'with', fields.length, 'fields');
    } else {
      console.error('❌ CoreLayoutBuilderService: Section not found for field reorder:', sectionId);
    }
    return updatedSections;
  }

  /**
   * Xử lý field properties update logic
   * ✅ REFACTORED: Pure function with sections parameter
   * ✅ FIXED: Support both _id and id for backward compatibility
   */
  handleFieldPropertiesUpdate(sections: Section[], sectionId: string, fieldId: string | number, updatedField: Field): Section[] {
    const updatedSections = [...sections];
    // ✅ FIXED: Find section by both _id and id for backward compatibility
    const sectionIndex = updatedSections.findIndex(s => (s._id) === sectionId);

    if (sectionIndex !== -1) {
      const fields = [...updatedSections[sectionIndex].fields];
      const fieldIndex = fields.findIndex(f => f._id === fieldId);

      if (fieldIndex !== -1) {
        fields[fieldIndex] = { ...updatedField };
        updatedSections[sectionIndex] = { ...updatedSections[sectionIndex], fields };
        console.log('✅ CoreLayoutBuilderService: Field properties updated:', sectionId, fieldId);
      } else {
        console.error('❌ CoreLayoutBuilderService: Field not found for properties update:', fieldId);
      }
    } else {
      console.error('❌ CoreLayoutBuilderService: Section not found for field properties update:', sectionId);
    }

    return updatedSections;
  }

  /**
   * Xử lý field deletion logic
   * ✅ REFACTORED: Pure function with sections parameter
   */
  handleFieldDeletion(sections: Section[], sectionId: string, fieldId: number): Section[] {
    const updatedSections = [...sections];
    const sectionIndex = updatedSections.findIndex(s => s._id === sectionId);

    if (sectionIndex !== -1) {
      const fields = updatedSections[sectionIndex].fields.filter(f => f._id !== fieldId.toString());
      updatedSections[sectionIndex] = { ...updatedSections[sectionIndex], fields };
      console.log('✅ CoreLayoutBuilderService: Field deleted:', sectionId, fieldId);
    } else {
      console.error('❌ CoreLayoutBuilderService: Section not found for field deletion:', sectionId);
    }

    return updatedSections;
  }

  /**
   * Xử lý quick add field logic
   * ✅ REFACTORED: Pure function with sections parameter
   * ✅ UPDATED: Thêm tham số defaultPermissionProfiles
   */
  handleQuickAddField(sections: Section[], sectionId: string, fieldType: string, defaultPermissionProfiles?: FieldPermissionProfile[]): Section[] {
    const sectionIndex = sections.findIndex(s => s._id === sectionId);
    if (sectionIndex === -1) {
      console.error('❌ CoreLayoutBuilderService: Section not found for quick add field:', sectionId);
      return sections;
    }

    const fieldTemplate = this.getFieldTypeTemplate(fieldType);
    if (!fieldTemplate) {
      console.error('❌ CoreLayoutBuilderService: Field type template not found:', fieldType);
      return sections;
    }

    const fieldId = this.generateFieldId();
    const newField: Field = {
      ...fieldTemplate,
      _id: fieldId,
      // Khởi tạo permissionProfiles với giá trị mặc định
      permissionProfiles: defaultPermissionProfiles ? [...defaultPermissionProfiles] : []
      //order: sections[sectionIndex].fields.length + 1
    };

    const updatedSections = [...sections];
    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: [...updatedSections[sectionIndex].fields, newField]
    };

    console.log('✅ CoreLayoutBuilderService: Quick field added:', sectionId, fieldType);
    return updatedSections;
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Tạo ID duy nhất cho section
   * ✅ MOVED FROM DynamicLayoutBuilderService: ID generation utility
   */
  private generateSectionId(): string {
    return 'section_' + Date.now().toString() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Tạo ID duy nhất cho field
   * ✅ MOVED FROM DynamicLayoutBuilderService: ID generation utility
   */
  private generateFieldId(): string {
    return `field_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  /**
   * Tạo ID duy nhất chung
   * ✅ MOVED FROM DynamicLayoutBuilderService: ID generation utility
   */
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }

  // ==================== TEMPLATE MANAGEMENT ====================

  /**
   * Áp dụng template và trả về sections mới
   * ✅ REFACTORED: Pure function - no state management
   */
  applyTemplateAndGetSections(template: Template): Section[] {
    console.log('🔄 CoreLayoutBuilderService: Processing template for sections:', template.name);

    // Tạo sections mới với IDs duy nhất
    const newSections: Section[] = template.sections.map(section => ({
      ...section,
      _id: this.generateSectionId(),
      // id: this.generateSectionId(), // Removed - using _id only
      fields: section.fields.map(field => {
        const fieldId = this.generateFieldId();
        return {
          ...field,
          _id: fieldId
        };
      })
    }));

    console.log('✅ CoreLayoutBuilderService: Template processed:', newSections.length, 'sections');
    return newSections;
  }

  /**
   * Lấy templates ngành hàng mặc định
   * ✅ MOVED FROM DynamicLayoutBuilderService: Default templates provider
   */
  getDefaultTemplates(): Template[] {
    // console.log('📋 CoreLayoutBuilderService: Getting default templates');

    return [
      {
        name: 'Thời trang',
        sections: [
          {
            _id: this.generateSectionId(),
            // id: this.generateSectionId(), // Removed - using _id only
            title: 'Thông tin cơ bản',
            fields: [
              { _id: this.generateFieldId(), type: 'text', label: 'Tên khách hàng', value: '', isRequired: true, order: 1, permissionProfiles: [] },
              { _id: this.generateFieldId(), type: 'email', label: 'Email', value: '', isRequired: true, order: 2, permissionProfiles: [] },
              { _id: this.generateFieldId(), type: 'phone', label: 'Số điện thoại', value: '', isRequired: false, order: 3, permissionProfiles: [] }
            ]
          },
          {
            _id: this.generateSectionId(),
            // id: this.generateSectionId(), // Removed - using _id only
            title: 'Thông tin thời trang',
            fields: [
              { _id: this.generateFieldId(), type: 'picklist', label: 'Kích cỡ', value: '', constraints: { picklistValues: ['S', 'M', 'L', 'XL'] }, order: 1, permissionProfiles: [] },
              { _id: this.generateFieldId(), type: 'picklist', label: 'Màu sắc', value: '', constraints: { picklistValues: ['Đỏ', 'Xanh', 'Vàng', 'Đen', 'Trắng'] }, order: 2, permissionProfiles: [] }
            ]
          }
        ]
      },
      {
        name: 'Mỹ phẩm',
        sections: [
          {
            _id: this.generateSectionId(),
            // id: this.generateSectionId(), // Removed - using _id only
            title: 'Thông tin cơ bản',
            fields: [
              { _id: this.generateFieldId(), type: 'text', label: 'Tên khách hàng', value: '', isRequired: true, order: 1, permissionProfiles: [] },
              { _id: this.generateFieldId(), type: 'email', label: 'Email', value: '', isRequired: true, order: 2, permissionProfiles: [] }
            ]
          },
          {
            _id: this.generateSectionId(),
            // id: this.generateSectionId(), // Removed - using _id only
            title: 'Thông tin làn da',
            fields: [
              { _id: this.generateFieldId(), type: 'picklist', label: 'Loại da', value: '', constraints: { picklistValues: ['Da khô', 'Da dầu', 'Da hỗn hợp', 'Da nhạy cảm'] }, order: 1, permissionProfiles: [] },
              { _id: this.generateFieldId(), type: 'multi-picklist', label: 'Sản phẩm yêu thích', value: [], constraints: { picklistValues: ['Kem dưỡng', 'Serum', 'Toner', 'Cleanser'] }, order: 2, permissionProfiles: [] }
            ]
          }
        ]
      }
    ];
  }

  // ==================== FIELD TYPE MANAGEMENT ====================

  /**
   * Lấy field types mặc định
   * ✅ REFACTORED: Sử dụng DEFAULT_FIELD_TYPES từ constants
   */
  getDefaultFieldTypes(): Field[] {
    // console.log('📋 CoreLayoutBuilderService: Getting default field types from constants');
    return DEFAULT_FIELD_TYPES;
  }

  /**
   * Lấy template cho field type
   * ✅ REFACTORED: Sử dụng getFieldTypeById từ constants
   */
  getFieldTypeTemplate(fieldType: string): Field | null {
    // console.log('🔍 CoreLayoutBuilderService: Getting field type template for:', fieldType);

    // ✅ REFACTORED: Sử dụng getFieldTypeById từ constants thay vì hardcode
    return getFieldTypeById(fieldType as FieldType);
  }

  /**
   * Tạo field mới từ field type với ID và order
   * ✅ REFACTORED: Sử dụng constants và temporary ID logic mới
   * ✅ UPDATED: Thêm tham số defaultPermissionProfiles để khởi tạo permissionProfiles
   */
  createFieldFromType(fieldType: string, order: number, defaultPermissionProfiles?: FieldPermissionProfile[]): Field | null {
    // console.log('🔄 CoreLayoutBuilderService: Creating field from type:', fieldType, 'order:', order);

    // Sử dụng getFieldTypeById từ constants thay vì getFieldTypeTemplate
    const template = getFieldTypeById(fieldType as FieldType);
    if (template) {
      // Generate temporary ID khi tạo field mới
      const tempId = generateTempFieldId();
      const result = {
        ...template,
        _id: tempId, // Generate _id cho field mới
        order,
        // Khởi tạo permissionProfiles với giá trị mặc định từ layout config
        permissionProfiles: defaultPermissionProfiles ? [...defaultPermissionProfiles] : []
      };
      // console.log('✅ CoreLayoutBuilderService: Field created from type with temp ID:', tempId);
      return result;
    }

    // console.error('❌ CoreLayoutBuilderService: Failed to create field from type:', fieldType);
    return null;
  }

  // ==================== LAYOUT OPERATIONS ====================
  /**
   * Save current layout with business logic validation
   * ✅ REFACTORED: Pure function - no state management
   */
  saveCurrentLayoutData(layout: DynamicLayoutConfig, sections: Section[]): DynamicLayoutConfig {
    // console.log('💾 CoreLayoutBuilderService: Saving layout data');

    if (!layout || !layout._id) {
      console.error('❌ CoreLayoutBuilderService: Cannot save layout: invalid layout or missing ID');
      return layout;
    }

    // console.log('💾 CoreLayoutBuilderService: Saving layout:', layout.title, 'with', sections.length, 'sections');

    // Apply business logic validation
    const validatedSections = this.validateSectionsForSave(sections);

    // Update layout with new sections
    const updatedLayout: DynamicLayoutConfig = {
      ...layout,
      sections: validatedSections
    };

    // Save to localStorage
    this.saveLayoutToStorage(updatedLayout);

    // console.log('✅ CoreLayoutBuilderService: Layout saved successfully');
    return updatedLayout;
  }

  /**
   * Validate sections before saving
   * ✅ REFACTORED: Pure function
   */
  validateSectionsForSave(sections: Section[]): Section[] {
    // console.log('🔍 CoreLayoutBuilderService: Validating sections for save');

    return sections.map(section => ({
      ...section,
      // Ensure all fields have proper IDs and order
      fields: section.fields.map((field, index) => ({
        ...field,
        order: index + 1,
        _id: field._id || `field_${Date.now()}_${index}`
      }))
    }));
  }

  /**
   * Save layout to localStorage
   * ✅ REFACTORED: Pure function
   */
  saveLayoutToStorage(layout: DynamicLayoutConfig): void {
    // console.log('💾 CoreLayoutBuilderService: Saving layout to storage:', layout._id);

    try {
      const storageKey = `dynamic-layout-${layout._id}`;
      localStorage.setItem(storageKey, JSON.stringify(layout));
      localStorage.setItem('dynamic-layout-builder-current-layout', layout._id || '');
      console.log('✅ CoreLayoutBuilderService: Layout saved to storage successfully');
    } catch (error) {
      console.error('❌ CoreLayoutBuilderService: Error saving layout to storage:', error);
    }
  }

  /**
   * Switch to layout business logic
   * ✅ REFACTORED: Pure function - no state management
   */
  switchToLayoutBusinessLogic(
    layoutIndex: number,
    layouts: DynamicLayoutConfig[]
  ): { isValid: boolean; newLayout: DynamicLayoutConfig | null } {
    console.log('🔄 CoreLayoutBuilderService: Switching to layout at index:', layoutIndex);

    // Validation
    if (layoutIndex < 0 || layoutIndex >= layouts.length) {
      console.error('❌ CoreLayoutBuilderService: Invalid layout index:', layoutIndex);
      return { isValid: false, newLayout: null };
    }

    const newLayout = layouts[layoutIndex];

    // Persistence logic
    if (newLayout?._id) {
      localStorage.setItem('dynamic-layout-builder-current-layout', newLayout._id);
      console.log('💾 CoreLayoutBuilderService: Layout persisted to localStorage:', newLayout._id);
    }

    console.log('✅ CoreLayoutBuilderService: Layout switch business logic completed:', {
      _id: newLayout._id,
      title: newLayout.title,
      sectionsCount: newLayout.sections?.length || 0
    });

    return { isValid: true, newLayout };
  }

  /**
   * Field reordering business logic
   * ✅ REFACTORED: Pure function with sections parameter
   */
  handleFieldReorderBusinessLogic(
    sections: Section[],
    sectionId: string,
    fields: Field[]
  ): Section[] {
    return sections.map(section => {
      if ((section._id) === sectionId) {
        return {
          ...section,
          fields: fields.map((field, index) => ({
            ...field,
            order: index + 1
          }))
        };
      }
      return section;
    });
  }

  // ==================== STORAGE OPERATIONS ====================

  /**
   * Lưu layout vào localStorage
   * ✅ REFACTORED: Pure function - no state management
   */
  saveLayout(sections: Section[], instanceId?: string): Observable<boolean> {
    console.log('💾 CoreLayoutBuilderService: Saving layout with', sections.length, 'sections');

    return of(true).pipe(
      delay(1000), // Mô phỏng API call
      tap(() => {
        try {
          const layoutData = {
            sections,
            savedAt: new Date().toISOString(),
            version: '1.0'
          };

          const storageKey = instanceId ? `dynamic-layout-builder-config-${instanceId}` : 'dynamic-layout-builder-config';
          localStorage.setItem(storageKey, JSON.stringify(layoutData));

          console.log('✅ CoreLayoutBuilderService: Layout saved successfully', layoutData);
        } catch (error) {
          console.error('❌ CoreLayoutBuilderService: Error saving layout:', error);
          throw error; // Let caller handle error
        }
      })
    );
  }

  /**
   * Gửi layout qua API (mô phỏng)
   * ✅ REFACTORED: Pure function - no state management
   */
  submitLayout(sections: Section[]): Observable<SubmitLayoutResponse> {
    console.log('🚀 CoreLayoutBuilderService: Submitting layout with', sections.length, 'sections');

    const payload = {
      layout: {
        sections,
        metadata: {
          createdAt: new Date().toISOString(),
          version: '1.0',
          totalFields: sections.reduce((total, section) => total + section.fields.length, 0)
        }
      }
    };

    return of({ success: true, id: this.generateId() }).pipe(
      delay(2000), // Mô phỏng API call
      tap(response => {
        console.log('✅ CoreLayoutBuilderService: Layout submitted successfully:', payload);
        console.log('✅ CoreLayoutBuilderService: API Response:', response);
      })
    );
  }

}
